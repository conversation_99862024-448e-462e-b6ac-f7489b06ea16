// Default API configuration for Eldorado
const ELDORADO_API_CONFIG = {
  baseUrl: "/api/eldorado", // Use proxy endpoint to bypass CORS
  defaultHeaders: {
    "Content-Type": "application/json",
  },
};

// Hardcoded Eldorado cookies for authentication (temporary)
const ELDORADO_COOKIES = {
  pseudoId: "9a181420-6331-488b-926b-ef93bb9c3737",
  xsrfToken: "d0760bfcb9a38dfe2698590b35a5612e950e7354cf58d33d45e1afff2fe9253f",
  locale: "en-US",
  intercomDeviceId: "92ecc443-199e-4616-b2a3-1b95d2d052e8",
  currencyPreference: "USD",
  intercomId: "5ed0fce8-1501-4553-ac48-2db6d47d2cb6",
  rtkclickidStore: "6875656a4550c6955ab907a8",
  cfClearance: "HVcjdxjctcro595LwfyQJyFamxQGcV9gkX25Obw_1WM-1752832959-1.2.1.1-wI9_oM.Dy.Bkv_ZjGf1kT0FfOKdZLJojMIWf5a_yfPCZTGhtWlbRzx71vtjC7CkuNCPWRkg7PplyhtRxYfrKE2djYvJ75KPbXD0Y1aZp6rV1eas.C6gi2fiZW30e1YEbU_WRl0TPSqlyFvvMEVVqBVtdVtSev6ztAZmiSjsBB_jvLwGG3eYJaeJHI0RvS_1eKBV5MlVFkO6rMlivqNIgr.24SfzvclAeLQt0L6mEpU0",
  refreshToken: "eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.yRZchqSXS2nSPNgbLvzGQLvXNUJvDSV1GSfL25KIeKBe8FN_zHhi4OBI7foDZGYNzXyavUDb7rzhbL-p6PdMwVZhTxQfxxvaxUxpIJAY6TcPTsL-J2Ce3qEgjSFxEV_pzt2glK09nHoKuKD-ywV9dSsUbCxhl04W81vb9tDcdkwWcvUcZWZf67VYqQCs14gb_7mCt7qdSApSigEZENpMdRnhUvmUnXU6aMxd7B4xtHZp7ZAdLobVO-hWB4TmxHXV3vAJ0Dp0sxau2h0ue29BPib0Hot-qI6YDRjDX_I9gC8RUnQUUMkXVKNghqlw97u4KmYtY5ZVe3jbGZEEjxOUNw.SHFBBa6NXdb48mHn.2sSJKzDR-e5zyFMlx8sdErAaBDxBI4EwBlpVHS-3DWCvLpnv7Mt3WOHJwpXeui_mfn2qYJGx_PIkpKTL_1EDIuAtzG40nIqNJhQniq3HDGgF119Pci8yV1--EKO8VzJLWNTTvvN3sJiYb1qLkgAbOj2BIuOZiVRiFqYVY_1Sdk9GSsyC6mi4Pbzs0m366vUEK2ZQWtNGU6BdyRlKdEXmymRHrVOEuy5CgzxlitdGyEQisxhtFkmn7MiiouL2IjlKChukv3U1g32LSN5RltQXlNa2lcEAZeNNFR8Avk2lS9vud8H3rNBGNkP36CNLTpCKrkuH3tXozWDZmsGWAmrP7HjNRwfUN6fdyZ7KlPZZEIJnUahLCu_137mqbE0LyJfLeRJu3Wos6gMYiKTtj3fuPoXr-YejgBEmriYTJlBMndpx0X2lhIFBdY0_DkOcSg8RfAyzQ0LY2Tdb4CkW26Z6OkIoTOcPxffZ32R7qKYwwVdDVVSFWlw66qnow8B2M_DATNPtKR93KhTn0vRTDcZUT5lb46JwcY2_0RPYD_cci4ESvr60o23-GUOpVKa3LFmyIXgjHHgfOOXEqgf0gp0Vn3MSCW8xKdK8Jr3o30AhXLU8mIaX10BvktY5CZqhMgYUS2E_XPWhAKk9ZdhVetXRzytWWv1jiem0BJY5B5XE5oKGLl0FLaW6pgrJemrLJfA94ZNqk94aTcHchazVRE2HQftbT6ndKGhcAPC2L-pGkptCbGaIEBl_01ytfM5bHf2qPyemn6RVFkU6SI_EoA52b2ydQipZQBhPhkmXXxw5xDcz9zediM50q2pjdpeUtlYoDkp2_7m98b9I5_DK9Er6JCOard53ABPaKIC7Fh0Y_v7mqyPGswgRShPWJrLcgR1KvLzO4_nDA5-TIUyQZP3Zjjdd1IdDYbazigEBMonfqiFxZxymqt-3i-VKNUoCktY__n6DdmjbYNEGkSjTXJTtkDou2osOxvG29_lxrXSp-tiFt3nCeZPhpxUZc7qrP4NsVyuw_LMGXF8-RegyRBEsu16ogT5sDTE3rXwdy4GkfLr9-y-ESrfRb1HYd-Rh1QU4gbIH2JBrp20rgN9u_dc7RBGj0zvct1a8sz2HqsJatGITJHgH0RKf-0SKdGnn2AFC1Tuw6O07va0GiYXnfmYhfslxQmw4WpyTua0ZsGEWTH2Q2gzPIykPsxSH3AsbN0Fh84Zlanff0yh0FX_NMePwf8QBJq9BrQ-73mV0DjTFIIAge4RXvt4KatZKxfpwYuAk2wBjEr5FD2snOA.RNZ5CXVl4SazW_glPEhYJA",
  idToken: "eyJraWQiOiJETTJSdklPTldaZThEd01ZNDNlbHZDTE9mbmZVNFozcWljOFQ4bmhTbmFBPSIsImFsZyI6IlJTMjU2In0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.GB6fXuJCI1vkT69vHpYKlddussYKgeKnRMk7OTNmBn4EFrOTtyh5FubABQOHgPS7TPM8P9p_mdsHZR-tvH7HJu8WDMkp9gBZUbDa7iZ_jgUfq5O8JuKVgvcRBDj_vYxWqL_Xv1P-8eFpP8pzAUFSoY5AzSQqUGv_vfs6ILgssPwfd90mmipFctse7nAf4MivK7v1bZbQJ5TQMqKvIoy12Ic-Fmr1mQxqlUjSiABP7-hWJVyP9gnx69B7zbFvkrCpMcbaM0ETQh0s_-2N24rosrBDnLrIJ06nn7qgOGTc5akKuXeGinxVm-yzkUsnPYVLMj4UL11XJgE-uZYlsfcgRA"
};

// Helper function to build cookie string for requests
const buildCookieString = () => {
  return [
    `pseudoId=${ELDORADO_COOKIES.pseudoId}`,
    `__Host-XSRF-TOKEN=${ELDORADO_COOKIES.xsrfToken}`,
    `eldoradogg_locale=${ELDORADO_COOKIES.locale}`,
    `intercom-device-id-mipk5r3a=${ELDORADO_COOKIES.intercomDeviceId}`,
    `eldoradogg_currencyPreference=${ELDORADO_COOKIES.currencyPreference}`,
    `intercom-id-mipk5r3a=${ELDORADO_COOKIES.intercomId}`,
    `rtkclickid-store=${ELDORADO_COOKIES.rtkclickidStore}`,
    `cf_clearance=${ELDORADO_COOKIES.cfClearance}`,
    `__Host-EldoradoRefreshToken=${ELDORADO_COOKIES.refreshToken}`,
    `__Host-EldoradoIdToken=${ELDORADO_COOKIES.idToken}`
  ].join('; ');
};

// Helper function to get auth headers for cookie-based authentication
const getEldoradoAuthHeaders = () => {
  const headers: Record<string, string> = {
    ...ELDORADO_API_CONFIG.defaultHeaders,
    // Add XSRF token for CSRF protection
    "X-XSRF-TOKEN": ELDORADO_COOKIES.xsrfToken,
    // Add cookies manually
    "Cookie": buildCookieString(),
    // Add additional headers for proxy
    "X-Requested-With": "XMLHttpRequest",
  };

  return headers;
};

// Interfaces for the flexible offers search API
interface TradeEnvironmentValue {
  value: string;
  name: string;
  id: string;
  imageLocation: string | null;
}

interface OfferAttributeIdValue {
  value: string;
  name: string;
  id: string;
  imageLocation: string | null;
}

interface OfferImage {
  smallImage: string;
  largeImage: string;
  originalSizeImage: string;
}

interface PriceAmount {
  amount: number;
  currency: string;
}

interface ExchangeRate {
  currency: string;
  exchangeRate: number;
}

interface FlexibleOffer {
  tradeEnvironmentValues: TradeEnvironmentValue[];
  offerAttributeIdValues: OfferAttributeIdValue[];
  offerTitle: string;
  offerTitleOriginal: string;
  mainOfferImage: OfferImage;
  offerImages: OfferImage[];
  tags: string[];
  id: string;
  userId: string;
  gameId: string;
  category: string;
  gameCategoryTitle: string;
  gameSeoAlias: string;
  description: string;
  quantity: number;
  minQuantity: number;
  volumeDiscounts: any[];
  pricePerUnit: PriceAmount;
  pricePerUnitWithDiscount: PriceAmount | null;
  discountPercentage: number | null;
  guaranteedDeliveryTime: string;
  expireDate: string;
  offerState: string;
  version: string;
  exchangeRate: ExchangeRate;
  pricePerUnitInUSD: PriceAmount;
}

export interface FlexibleOffersSearchResponse {
  pageIndex: number;
  totalPages: number;
  recordCount: number;
  pageSize: number;
  results: FlexibleOffer[];
}

// Parameters for listing current offers
export interface ListCurrentOffersParams {
  pageIndex?: number;
  pageSize?: number;
  category?: string;
}

/**
 * Eldorado offers service
 */
export const eldoradoOffersService = {
  /**
   * List current offers for the authenticated user
   *
   * @param pageIndex Current page index (default: 1)
   * @param pageSize Number of items per page (default: 40)
   * @param category Filter by category (default: "Account")
   * @returns Promise with the current offers data
   */
  listCurrentOffers: async ({
    pageIndex = 1,
    pageSize = 40,
    category = "Account",
  }: ListCurrentOffersParams = {}): Promise<FlexibleOffersSearchResponse> => {
    try {
      const url = new URL(`${ELDORADO_API_CONFIG.baseUrl}/flexibleOffers/me/search`);

      // Add query parameters
      url.searchParams.append("pageIndex", pageIndex.toString());
      url.searchParams.append("pageSize", pageSize.toString());
      url.searchParams.append("category", category);

      const response = await fetch(url.toString(), {
        method: "GET",
        headers: getEldoradoAuthHeaders(),
      });

      if (!response.ok) {
        throw new Error(`Eldorado API error: ${response.status} - ${response.statusText}`);
      }

      return (await response.json()) as FlexibleOffersSearchResponse;
    } catch (error) {
      console.error("Failed to fetch current Eldorado offers:", error);
      throw error;
    }
  },
};

export default {
  offers: eldoradoOffersService,
};